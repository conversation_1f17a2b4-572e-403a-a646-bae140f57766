package com.voice.service;

import com.voice.dto.SynthesisRequest;
import com.voice.entity.SynthesisRecord;
import com.voice.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 语音合成服务类
 */
@Service
public class VoiceSynthesisService {

    @Autowired
    private UserService userService;

    @Autowired
    private SynthesisRecordService synthesisRecordService;

    @Autowired
    @Qualifier("simpleTimeoutRestTemplate")
    private RestTemplate restTemplate;

    @Value("${voice.gpt-sovits.api-url}")
    private String gptSovitsApiUrl;

    @Value("${voice.gpt-sovits.timeout}")
    private Integer gptSovitsTimeout;

    @Value("${voice.file.audio-path}")
    private String audioPath;

    @Value("${voice.synthesis.cost-points}")
    private Integer costPoints;

    /**
     * 语音合成
     */
    @Transactional
    public Map<String, Object> synthesizeVoice(Long userId, SynthesisRequest request) {
        // 检查用户积分
        User user = userService.getById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        if (user.getPoints() < costPoints) {
            throw new RuntimeException("积分不足，需要" + costPoints + "积分");
        }

        // 创建合成记录
        SynthesisRecord record = new SynthesisRecord();
        record.setUserId(userId);
        record.setText(request.getText());
        record.setModelName(request.getModelName());
        record.setPointsCost(costPoints);
        record.setStatus(2); // 处理中
        record.setCreateTime(LocalDateTime.now());
        synthesisRecordService.save(record);

        try {
            // 调用GPT-SoVITS API
            Map<String, Object> apiRequest = new HashMap<>();
            apiRequest.put("text", request.getText());
            apiRequest.put("model_name", request.getModelName());
            apiRequest.put("media_type", request.getMediaType());
            apiRequest.put("streaming_mode", request.getStreamingMode());
            apiRequest.put("speed_factor", request.getSpeedFactor());

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(apiRequest, headers);

            ResponseEntity<byte[]> response = restTemplate.postForEntity(
                gptSovitsApiUrl + "/voice", entity, byte[].class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                // 保存音频文件
                String fileName = UUID.randomUUID().toString() + ".wav";
                String filePath = audioPath + fileName;
                
                // 确保目录存在
                File directory = new File(audioPath);
                if (!directory.exists()) {
                    directory.mkdirs();
                }

                try (FileOutputStream fos = new FileOutputStream(filePath)) {
                    fos.write(response.getBody());
                }

                // 更新记录
                record.setAudioUrl("/audio/" + fileName);
                record.setFileSize((long) response.getBody().length);
                record.setStatus(1); // 成功
                synthesisRecordService.updateById(record);

                // 扣除积分
                userService.deductPoints(userId, costPoints, "synthesis", "语音合成消耗积分");

                Map<String, Object> result = new HashMap<>();
                result.put("audioUrl", record.getAudioUrl());
                result.put("fileSize", record.getFileSize());
                result.put("recordId", record.getId());
                return result;

            } else {
                throw new RuntimeException("语音合成失败");
            }

        } catch (Exception e) {
            // 更新记录为失败状态
            record.setStatus(0);
            record.setErrorMessage(e.getMessage());
            synthesisRecordService.updateById(record);
            
            throw new RuntimeException("语音合成失败: " + e.getMessage());
        }
    }

    /**
     * 获取可用模型列表
     */
    public Map<String, Object> getAvailableModels() {
        try {
            ResponseEntity<Map> response = restTemplate.getForEntity(
                gptSovitsApiUrl + "/models", Map.class);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                return response.getBody();
            } else {
                throw new RuntimeException("获取模型列表失败");
            }
        } catch (Exception e) {
            throw new RuntimeException("获取模型列表失败: " + e.getMessage());
        }
    }
}
