package com.voice.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.voice.entity.SynthesisRecord;
import com.voice.mapper.SynthesisRecordMapper;
import org.springframework.stereotype.Service;

/**
 * 语音合成记录服务类
 */
@Service
public class SynthesisRecordService extends ServiceImpl<SynthesisRecordMapper, SynthesisRecord> {

    /**
     * 分页查询用户合成记录
     */
    public IPage<SynthesisRecord> getUserSynthesisRecords(Long userId, int current, int size) {
        Page<SynthesisRecord> page = new Page<>(current, size);
        QueryWrapper<SynthesisRecord> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId);
        wrapper.orderByDesc("create_time");
        
        return this.page(page, wrapper);
    }

    /**
     * 分页查询所有合成记录
     */
    public IPage<SynthesisRecord> getSynthesisRecordPage(int current, int size, Long userId, Integer status) {
        Page<SynthesisRecord> page = new Page<>(current, size);
        QueryWrapper<SynthesisRecord> wrapper = new QueryWrapper<>();

        if (userId != null) {
            wrapper.eq("user_id", userId);
        }

        if (status != null) {
            wrapper.eq("status", status);
        }

        wrapper.orderByDesc("create_time");
        return this.page(page, wrapper);
    }

    /**
     * 获取用户合成次数统计
     */
    public int getUserSynthesisCount(Long userId) {
        QueryWrapper<SynthesisRecord> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId);
        wrapper.eq("status", 1); // 只统计成功的合成记录

        return Math.toIntExact(this.count(wrapper));
    }
}
