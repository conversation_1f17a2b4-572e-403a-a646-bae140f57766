<template>
  <div class="synthesis-container">

    <!-- 主合成区域 -->
    <div class="synthesis-main">
      <div class="synthesis-card">
        <div class="card-glow"></div>
        <div class="card-header">
          <div class="header-left">
            <div class="header-icon">
              <i class="el-icon-microphone"></i>
            </div>
            <h2 class="header-title">语音合成</h2>
          </div>
          <div class="points-indicator">
            <div class="points-icon">
              <i class="el-icon-coin"></i>
            </div>
            <div class="points-info">
              <span class="points-value">{{ userPoints }}</span>
              <span class="points-label">积分</span>
            </div>
          </div>
        </div>

        <div class="synthesis-form">
          <el-form ref="synthesisForm" :model="synthesisForm" :rules="rules" label-width="0">
            <!-- 文本输入区域 -->
            <div class="form-section">
              <div class="section-header">
                <i class="el-icon-edit-outline"></i>
                <span>合成文本</span>
              </div>
              <div class="text-input-wrapper">
                <el-input
                  type="textarea"
                  :rows="6"
                  placeholder="请输入要合成的文本内容，最多500个字符..."
                  v-model="synthesisForm.text"
                  maxlength="500"
                  show-word-limit
                  class="tech-textarea">
                </el-input>
              </div>
            </div>

            <!-- 模型选择区域 -->
            <div class="form-section">
              <div class="section-header">
                <i class="el-icon-cpu"></i>
                <span>语音模型</span>
              </div>
              <div class="model-selector">
                <el-select
                  v-model="synthesisForm.modelName"
                  placeholder="请选择语音模型"
                  class="tech-select">
                  <el-option
                    v-for="model in models"
                    :key="model.name"
                    :label="model.name"
                    :value="model.name">
                    <div class="model-option">
                      <span class="model-name">{{ model.name }}</span>
                      <span class="model-desc">{{ model.text_lang || '高质量语音合成' }}</span>
                    </div>
                  </el-option>
                </el-select>
              </div>
            </div>

            <!-- 格式选择区域 -->
            <div class="form-section">
              <div class="section-header">
                <i class="el-icon-headset"></i>
                <span>音频格式</span>
              </div>
              <div class="format-selector">
                <div
                  class="format-option"
                  :class="{ active: synthesisForm.mediaType === 'wav' }"
                  @click="synthesisForm.mediaType = 'wav'">
                  <div class="format-icon">
                    <i class="el-icon-video-camera"></i>
                  </div>
                  <div class="format-info">
                    <span class="format-name">WAV</span>
                    <span class="format-desc">无损音质</span>
                  </div>
                </div>
                <div
                  class="format-option"
                  :class="{ active: synthesisForm.mediaType === 'mp3' }"
                  @click="synthesisForm.mediaType = 'mp3'">
                  <div class="format-icon">
                    <i class="el-icon-headset"></i>
                  </div>
                  <div class="format-info">
                    <span class="format-name">MP3</span>
                    <span class="format-desc">压缩音质</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 语速控制区域 -->
            <div class="form-section">
              <div class="section-header">
                <i class="el-icon-timer"></i>
                <span>语速控制</span>
              </div>
              <div class="speed-control">
                <div class="speed-slider-wrapper">
                  <el-slider
                    v-model="synthesisForm.speedFactor"
                    :min="0.5"
                    :max="1.5"
                    :step="0.1"
                    :show-tooltip="true"
                    :format-tooltip="formatSpeedTooltip"
                    class="tech-slider">
                  </el-slider>
                </div>
                <div class="speed-display">
                  <span class="speed-label">当前语速:</span>
                  <span class="speed-value">{{ synthesisForm.speedFactor }}x</span>
                </div>
                <div class="speed-presets">
                  <button
                    class="preset-btn"
                    :class="{ active: synthesisForm.speedFactor === 0.8 }"
                    @click="synthesisForm.speedFactor = 0.8">
                    慢速
                  </button>
                  <button
                    class="preset-btn"
                    :class="{ active: synthesisForm.speedFactor === 1.0 }"
                    @click="synthesisForm.speedFactor = 1.0">
                    正常
                  </button>
                  <button
                    class="preset-btn"
                    :class="{ active: synthesisForm.speedFactor === 1.2 }"
                    @click="synthesisForm.speedFactor = 1.2">
                    快速
                  </button>
                </div>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="action-section">
              <button
                class="synthesis-btn"
                :class="{ disabled: userPoints < 10 || loading }"
                @click="handleSynthesis"
                :disabled="userPoints < 10 || loading">
                <div class="btn-content">
                  <i class="el-icon-microphone" v-if="!loading"></i>
                  <i class="el-icon-loading" v-if="loading"></i>
                  <span>{{ loading ? '合成中...' : '开始合成' }}</span>
                  <span class="cost-info">(消耗10积分)</span>
                </div>
                <div class="btn-glow"></div>
              </button>
              <button class="reset-btn" @click="resetForm">
                <i class="el-icon-refresh"></i>
                <span>重置</span>
              </button>
            </div>
          </el-form>
        </div>

        <!-- 合成结果 -->
        <div v-if="audioUrl" class="result-section">
          <div class="result-header">
            <i class="el-icon-success"></i>
            <span>合成完成</span>
          </div>
          <div class="audio-player-wrapper">
            <div class="audio-visualizer">
              <div class="wave-bars">
                <div class="bar" v-for="i in 20" :key="i"></div>
              </div>
            </div>
            <audio controls :src="audioUrl" class="tech-audio"></audio>
            <div class="audio-actions">
              <button class="download-btn" @click="downloadAudio">
                <i class="el-icon-download"></i>
                <span>下载音频</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 最近记录区域 -->
    <div class="records-section">
      <div class="records-card">
        <div class="card-header">
          <div class="header-left">
            <div class="header-icon">
              <i class="el-icon-document"></i>
            </div>
            <h3 class="header-title">最近合成记录</h3>
          </div>
          <button class="refresh-btn" @click="loadRecentRecords">
            <i class="el-icon-refresh"></i>
          </button>
        </div>

        <div class="records-list">
          <div v-if="recentRecords.length === 0" class="empty-state">
            <i class="el-icon-document-remove"></i>
            <p>暂无合成记录</p>
          </div>
          <div v-else class="record-items">
            <div
              v-for="record in recentRecords"
              :key="record.id"
              class="record-item">
              <div class="record-content">
                <div class="record-text">{{ record.text }}</div>
                <div class="record-meta">
                  <span class="model-tag">{{ record.modelName }}</span>
                  <span class="time">{{ record.createTime }}</span>
                </div>
              </div>
              <div class="record-status">
                <div class="status-indicator" :class="getStatusClass(record.status)">
                  <i :class="getStatusIcon(record.status)"></i>
                </div>
              </div>
              <div class="record-actions" v-if="record.audioUrl">
                <button class="action-btn play" @click="playAudio(record.audioUrl)">
                  <i class="el-icon-video-play"></i>
                </button>
                <button class="action-btn download" @click="downloadAudio(record.audioUrl)">
                  <i class="el-icon-download"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'Synthesis',
  data() {
    return {
      synthesisForm: {
        text: '',
        modelName: '',
        mediaType: 'wav',
        speedFactor: 1.0
      },
      rules: {
        text: [
          { required: true, message: '请输入合成文本', trigger: 'blur' },
          { max: 500, message: '文本长度不能超过500个字符', trigger: 'blur' }
        ],
        modelName: [
          { required: true, message: '请选择语音模型', trigger: 'change' }
        ]
      },
      loading: false,
      models: [],
      audioUrl: '',
      recentRecords: []
    }
  },
  computed: {
    ...mapGetters(['userPoints'])
  },
  methods: {
    async loadModels() {
      try {
        const response = await this.$api.voice.getModels()
        this.models = response.data.models || []
        if (this.models.length > 0) {
          this.synthesisForm.modelName = this.models[0].name
        }
      } catch (error) {
        console.error('加载模型列表失败:', error)
      }
    },

    async handleSynthesis() {
      this.$refs.synthesisForm.validate(async (valid) => {
        if (valid) {
          if (this.userPoints < 10) {
            this.$message.error('积分不足，需要10积分')
            return
          }

          this.loading = true
          try {
            const response = await this.$api.voice.synthesize(this.synthesisForm)
            this.audioUrl = `http://localhost:8080/api${response.data.audioUrl}`
            this.$message.success('语音合成成功')

            // 更新用户积分
            this.$store.dispatch('updateUserPoints', this.userPoints - 10)

            // 刷新最近记录
            this.loadRecentRecords()
          } catch (error) {
            console.error('语音合成失败:', error)
          } finally {
            this.loading = false
          }
        }
      })
    },

    resetForm() {
      this.$refs.synthesisForm.resetFields()
      this.audioUrl = ''
    },

    downloadAudio(url = this.audioUrl) {
      const link = document.createElement('a')
      link.href = url
      link.download = `synthesis_${Date.now()}.wav`
      link.click()
    },

    playAudio(url) {
      const audio = new Audio(url)
      audio.play()
    },

    async loadRecentRecords() {
      try {
        // 这里应该调用获取用户合成记录的API
        // const response = await this.$http.get('/user/synthesis-records?page=1&size=5')
        // this.recentRecords = response.data.records || []

        // 模拟数据
        this.recentRecords = [
          {
            id: 1,
            text: '这是一个测试文本，用于语音合成',
            modelName: 'zhexue_lao',
            status: 1,
            audioUrl: '/audio/test1.wav',
            createTime: '2023-12-01 10:30:00'
          },
          {
            id: 2,
            text: '另一个语音合成测试',
            modelName: 'xiaoshuo_nan',
            status: 1,
            audioUrl: '/audio/test2.wav',
            createTime: '2023-12-01 09:15:00'
          }
        ]
      } catch (error) {
        console.error('加载最近记录失败:', error)
      }
    },

    getStatusClass(status) {
      return {
        'success': status === 1,
        'error': status === 0,
        'processing': status === 2
      }
    },

    getStatusIcon(status) {
      switch (status) {
        case 1: return 'el-icon-success'
        case 0: return 'el-icon-error'
        case 2: return 'el-icon-loading'
        default: return 'el-icon-warning'
      }
    },

    formatSpeedTooltip(value) {
      return `${value}x`
    }
  },
  mounted() {
    this.loadModels()
    this.loadRecentRecords()
  }
}
</script>

<style scoped>
/* 全局容器 */
.synthesis-container {
  position: relative;
  padding: 40px;
}

/* 主合成区域 */
.synthesis-main {
  position: relative;
  z-index: 10;
  max-width: 800px;
  margin: 0 auto 40px;
}

.synthesis-card {
  position: relative;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.synthesis-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 25px 50px rgba(64, 158, 255, 0.2);
}

.card-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(64, 158, 255, 0.1) 0%,
    rgba(103, 194, 58, 0.1) 100%);
  opacity: 0.5;
  animation: cardPulse 4s ease-in-out infinite alternate;
}

@keyframes cardPulse {
  0% { opacity: 0.3; }
  100% { opacity: 0.7; }
}

/* 卡片头部 */
.card-header {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30px 40px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 2;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.header-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #409EFF, #67C23A);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 15px rgba(64, 158, 255, 0.3);
}

.header-icon i {
  font-size: 24px;
  color: white;
}

.header-title {
  font-size: 24px;
  font-weight: 700;
  color: #ffffff;
  margin: 0;
  text-shadow: 0 2px 10px rgba(64, 158, 255, 0.3);
}

.points-indicator {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 20px;
  background: rgba(64, 158, 255, 0.1);
  border-radius: 20px;
  border: 1px solid rgba(64, 158, 255, 0.3);
}

.points-icon {
  width: 35px;
  height: 35px;
  background: linear-gradient(135deg, #FFD700, #FFA500);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: coinSpin 2s linear infinite;
}

@keyframes coinSpin {
  0% { transform: rotateY(0deg); }
  100% { transform: rotateY(360deg); }
}

.points-icon i {
  color: white;
  font-size: 16px;
}

.points-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.points-value {
  font-size: 18px;
  font-weight: 700;
  color: #409EFF;
}

.points-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

/* 表单区域 */
.synthesis-form {
  position: relative;
  padding: 40px;
  z-index: 2;
}

.form-section {
  margin-bottom: 30px;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
  color: #409EFF;
  font-size: 16px;
  font-weight: 600;
}

.section-header i {
  font-size: 18px;
}

/* 文本输入 */
.text-input-wrapper {
  position: relative;
}

.tech-textarea {
  width: 100%;
}

.tech-textarea >>> .el-textarea__inner {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  color: #ffffff;
  font-size: 14px;
  padding: 15px;
  resize: none;
  transition: all 0.3s ease;
}

.tech-textarea >>> .el-textarea__inner:focus {
  border-color: #409EFF;
  box-shadow: 0 0 20px rgba(64, 158, 255, 0.3);
}

.tech-textarea >>> .el-textarea__inner::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

/* 模型选择器 */
.model-selector {
  position: relative;
}

.tech-select {
  width: 100%;
}

.tech-select >>> .el-input__inner {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  color: #ffffff;
  height: 50px;
  line-height: 50px;
  padding: 0 20px;
  transition: all 0.3s ease;
}

.tech-select >>> .el-input__inner:focus {
  border-color: #409EFF;
  box-shadow: 0 0 20px rgba(64, 158, 255, 0.3);
}

.tech-select >>> .el-input__suffix {
  color: rgba(255, 255, 255, 0.7);
}

.model-option {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.model-name {
  font-weight: 600;
  color: #333;
}

.model-desc {
  font-size: 12px;
  color: #666;
}

/* 格式选择器 */
.format-selector {
  display: flex;
  gap: 20px;
}

.format-option {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.format-option:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(64, 158, 255, 0.5);
}

.format-option.active {
  background: rgba(64, 158, 255, 0.2);
  border-color: #409EFF;
  box-shadow: 0 4px 15px rgba(64, 158, 255, 0.3);
}

.format-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #409EFF, #67C23A);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.format-icon i {
  font-size: 20px;
  color: white;
}

.format-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.format-name {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
}

.format-desc {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

/* 语速控制区域 */
.speed-control {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.speed-slider-wrapper {
  padding: 0 10px;
}

.tech-slider >>> .el-slider__runway {
  background: rgba(255, 255, 255, 0.2);
  height: 8px;
  border-radius: 4px;
}

.tech-slider >>> .el-slider__bar {
  background: linear-gradient(135deg, #409EFF, #67C23A);
  height: 8px;
  border-radius: 4px;
}

.tech-slider >>> .el-slider__button {
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg, #409EFF, #67C23A);
  border: 3px solid white;
  box-shadow: 0 4px 15px rgba(64, 158, 255, 0.3);
}

.tech-slider >>> .el-slider__button:hover {
  transform: scale(1.2);
}

.speed-display {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.speed-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
}

.speed-value {
  color: #409EFF;
  font-size: 18px;
  font-weight: 700;
  text-shadow: 0 2px 10px rgba(64, 158, 255, 0.3);
}

.speed-presets {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.preset-btn {
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.preset-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(64, 158, 255, 0.5);
  color: #409EFF;
}

.preset-btn.active {
  background: rgba(64, 158, 255, 0.2);
  border-color: #409EFF;
  color: #409EFF;
  box-shadow: 0 4px 15px rgba(64, 158, 255, 0.3);
}

/* 操作按钮 */
.action-section {
  display: flex;
  gap: 20px;
  justify-content: center;
  margin-top: 40px;
}

.synthesis-btn, .reset-btn {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15px 30px;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
}

.synthesis-btn {
  background: linear-gradient(135deg, #409EFF, #67C23A);
  color: white;
  box-shadow: 0 8px 25px rgba(64, 158, 255, 0.3);
  min-width: 200px;
}

.synthesis-btn:hover:not(.disabled) {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(64, 158, 255, 0.4);
}

.synthesis-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-content {
  display: flex;
  align-items: center;
  gap: 10px;
  z-index: 2;
  position: relative;
}

.cost-info {
  font-size: 12px;
  opacity: 0.8;
}

.btn-glow {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent);
  transition: left 0.5s ease;
}

.synthesis-btn:hover:not(.disabled) .btn-glow {
  left: 100%;
}

.reset-btn {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  gap: 8px;
}

.reset-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* 合成结果区域 */
.result-section {
  margin-top: 40px;
  padding: 30px 40px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  z-index: 2;
}

.result-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
  color: #67C23A;
  font-size: 18px;
  font-weight: 600;
}

.result-header i {
  font-size: 20px;
}

.audio-player-wrapper {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  padding: 25px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.audio-visualizer {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.wave-bars {
  display: flex;
  align-items: end;
  gap: 3px;
  height: 40px;
}

.bar {
  width: 4px;
  background: linear-gradient(to top, #409EFF, #67C23A);
  border-radius: 2px;
  animation: waveAnimation 1.5s ease-in-out infinite;
}

.bar:nth-child(odd) {
  animation-delay: 0.1s;
}

.bar:nth-child(even) {
  animation-delay: 0.3s;
}

@keyframes waveAnimation {
  0%, 100% { height: 10px; }
  50% { height: 30px; }
}

.tech-audio {
  width: 100%;
  height: 50px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  margin-bottom: 20px;
}

.audio-actions {
  display: flex;
  justify-content: center;
}

.download-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 25px;
  background: linear-gradient(135deg, #67C23A, #85CE61);
  color: white;
  border: none;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.download-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(103, 194, 58, 0.3);
}

/* 记录区域 */
.records-section {
  position: relative;
  z-index: 10;
  max-width: 800px;
  margin: 0 auto;
}

.records-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.records-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 25px 50px rgba(64, 158, 255, 0.2);
}

.records-card .card-header {
  padding: 25px 30px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.records-card .header-title {
  font-size: 20px;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
}

.refresh-btn {
  width: 40px;
  height: 40px;
  background: rgba(64, 158, 255, 0.2);
  border: 1px solid rgba(64, 158, 255, 0.3);
  border-radius: 50%;
  color: #409EFF;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.refresh-btn:hover {
  background: rgba(64, 158, 255, 0.3);
  transform: rotate(180deg);
}

.records-list {
  padding: 30px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: rgba(255, 255, 255, 0.5);
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 15px;
  display: block;
}

.empty-state p {
  font-size: 16px;
  margin: 0;
}

.record-items {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.record-item {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.record-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(5px);
}

.record-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.record-text {
  color: #ffffff;
  font-size: 14px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.record-meta {
  display: flex;
  align-items: center;
  gap: 15px;
}

.model-tag {
  padding: 4px 12px;
  background: rgba(64, 158, 255, 0.2);
  color: #409EFF;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.time {
  color: rgba(255, 255, 255, 0.5);
  font-size: 12px;
}

.record-status {
  display: flex;
  align-items: center;
}

.status-indicator {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.status-indicator.success {
  background: rgba(103, 194, 58, 0.2);
  color: #67C23A;
}

.status-indicator.error {
  background: rgba(245, 108, 108, 0.2);
  color: #F56C6C;
}

.status-indicator.processing {
  background: rgba(230, 162, 60, 0.2);
  color: #E6A23C;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.record-actions {
  display: flex;
  gap: 10px;
}

.action-btn {
  width: 35px;
  height: 35px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.action-btn.play {
  background: rgba(64, 158, 255, 0.2);
  color: #409EFF;
}

.action-btn.play:hover {
  background: rgba(64, 158, 255, 0.3);
  transform: scale(1.1);
}

.action-btn.download {
  background: rgba(103, 194, 58, 0.2);
  color: #67C23A;
}

.action-btn.download:hover {
  background: rgba(103, 194, 58, 0.3);
  transform: scale(1.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .synthesis-container {
    padding: 20px;
  }

  .card-header {
    padding: 20px 25px;
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }

  .synthesis-form {
    padding: 25px;
  }

  .format-selector {
    flex-direction: column;
    gap: 15px;
  }

  .action-section {
    flex-direction: column;
    align-items: center;
  }

  .record-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .record-actions {
    align-self: flex-end;
  }
}
</style>
