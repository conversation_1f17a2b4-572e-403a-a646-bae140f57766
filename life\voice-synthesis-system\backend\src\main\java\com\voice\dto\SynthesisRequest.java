package com.voice.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 语音合成请求DTO
 */
@Data
public class SynthesisRequest {
    
    @NotBlank(message = "合成文本不能为空")
    @Size(max = 500, message = "合成文本长度不能超过500个字符")
    private String text;
    
    @NotBlank(message = "模型名称不能为空")
    private String modelName;

    //语速
    private String speedFactor;
    
    private String mediaType = "wav";
    
    private Boolean streamingMode = false;
}
